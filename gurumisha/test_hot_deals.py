#!/usr/bin/env python3
"""
Test script for Hot Deals functionality
"""
import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gurumisha.settings')
django.setup()

from core.models import Car, HotDeal, CarBrand, CarModel, User, Vendor
from django.utils import timezone


def test_hot_deals():
    """Test hot deals creation and calculation"""
    print("Testing Hot Deals functionality...")
    
    try:
        # Get or create a test car
        brand, _ = CarBrand.objects.get_or_create(name="Test Brand", defaults={'is_active': True})
        model, _ = CarModel.objects.get_or_create(
            name="Test Model", 
            brand=brand,
            defaults={'body_type': 'sedan'}
        )
        
        # Get or create a test user and vendor
        user, _ = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                'username': 'testuser',
                'first_name': 'Test',
                'last_name': 'User',
                'role': 'vendor'
            }
        )
        
        vendor, _ = Vendor.objects.get_or_create(
            user=user,
            defaults={
                'company_name': 'Test Company',
                'phone': '+254700000000',
                'is_verified': True
            }
        )
        
        # Create or get a test car
        car, created = Car.objects.get_or_create(
            title="Test Car for Hot Deals",
            defaults={
                'brand': brand,
                'model': model,
                'vendor': vendor,
                'year': 2020,
                'price': Decimal('2000000.00'),  # 2M KSH
                'mileage': 50000,
                'fuel_type': 'petrol',
                'transmission': 'automatic',
                'condition': 'used',
                'description': 'Test car for hot deals functionality',
                'is_approved': True
            }
        )
        
        if created:
            print(f"✓ Created test car: {car.title}")
        else:
            print(f"✓ Using existing test car: {car.title}")
        
        # Test hot deal creation with percentage discount
        print("\n--- Testing Hot Deal Creation ---")
        
        # Clean up any existing hot deals for this car
        HotDeal.objects.filter(car=car).delete()
        
        hot_deal = HotDeal.objects.create(
            car=car,
            title=f'Hot Deal: {car.title}',
            description='Test hot deal with 15% discount',
            discount_type='percentage',
            discount_value=Decimal('15.00'),
            original_price=car.price,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            is_active=True,
            auto_activate=True
        )
        
        print(f"✓ Created hot deal: {hot_deal.title}")
        print(f"  Original Price: KSh {hot_deal.original_price:,.2f}")
        print(f"  Discount: {hot_deal.discount_value}%")
        print(f"  Discounted Price: KSh {hot_deal.discounted_price:,.2f}")
        print(f"  Savings: KSh {hot_deal.original_price - hot_deal.discounted_price:,.2f}")
        
        # Verify calculations
        expected_discount = (hot_deal.original_price * hot_deal.discount_value) / Decimal('100')
        expected_discounted_price = hot_deal.original_price - expected_discount
        
        if abs(hot_deal.discounted_price - expected_discounted_price) < Decimal('0.01'):
            print("✓ Price calculation is correct")
        else:
            print(f"✗ Price calculation error. Expected: {expected_discounted_price}, Got: {hot_deal.discounted_price}")
        
        # Test car status update
        car.refresh_from_db()
        if car.is_hot_deal:
            print("✓ Car hot deal status updated correctly")
        else:
            print("✗ Car hot deal status not updated")
        
        # Test time remaining calculation
        print(f"\n--- Testing Time Calculations ---")
        time_remaining = hot_deal.time_remaining()
        print(f"✓ Time remaining: {time_remaining}")
        
        is_active = hot_deal.is_currently_active()
        print(f"✓ Is currently active: {is_active}")
        
        # Test fixed discount
        print(f"\n--- Testing Fixed Discount ---")
        hot_deal.discount_type = 'fixed'
        hot_deal.discount_value = Decimal('300000.00')  # 300K KSH fixed discount
        hot_deal.save()
        
        print(f"  Fixed Discount: KSh {hot_deal.discount_value:,.2f}")
        print(f"  Discounted Price: KSh {hot_deal.discounted_price:,.2f}")
        
        # Verify fixed discount calculation
        expected_fixed_price = hot_deal.original_price - hot_deal.discount_value
        if abs(hot_deal.discounted_price - expected_fixed_price) < Decimal('0.01'):
            print("✓ Fixed discount calculation is correct")
        else:
            print(f"✗ Fixed discount calculation error. Expected: {expected_fixed_price}, Got: {hot_deal.discounted_price}")
        
        print(f"\n--- Hot Deals Test Completed Successfully ---")
        return True
        
    except Exception as e:
        print(f"✗ Error during hot deals test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_spare_parts_categories():
    """Test spare parts category hierarchy"""
    print("\n\nTesting Spare Parts Categories...")
    
    try:
        from core.models import SparePartCategory
        
        # Test parent categories
        parent_categories = SparePartCategory.objects.filter(parent__isnull=True)
        print(f"✓ Found {parent_categories.count()} parent categories")
        
        for parent in parent_categories[:3]:  # Test first 3
            subcategories = parent.subcategories.all()
            print(f"  {parent.name}: {subcategories.count()} subcategories")
            
            # Test a few subcategories
            for subcat in subcategories[:2]:
                print(f"    - {subcat.name}")
        
        # Test category string representation
        engine_system = SparePartCategory.objects.filter(name="Engine System").first()
        if engine_system:
            fuel_system = engine_system.subcategories.filter(name="Fuel System").first()
            if fuel_system:
                fuel_injectors = fuel_system.subcategories.filter(name="Fuel Injectors").first()
                if fuel_injectors:
                    print(f"✓ Category hierarchy: {fuel_injectors}")
                else:
                    print("✗ Could not find Fuel Injectors subcategory")
            else:
                print("✗ Could not find Fuel System subcategory")
        else:
            print("✗ Could not find Engine System parent category")
        
        print("✓ Spare Parts Categories test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Error during spare parts categories test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("GURUMISHA MOTORS - FEATURE TESTING")
    print("=" * 60)
    
    success = True
    
    # Test hot deals
    if not test_hot_deals():
        success = False
    
    # Test spare parts categories
    if not test_spare_parts_categories():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED SUCCESSFULLY!")
    else:
        print("❌ SOME TESTS FAILED")
    print("=" * 60)
