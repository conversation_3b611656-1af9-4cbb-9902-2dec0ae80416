#!/usr/bin/env python3
"""
Gurumisha Django E-commerce Platform Server
Standalone server script with admin management and 4-day kill switch

This server provides:
- Django application hosting
- Admin user management
- Custom messaging capabilities
- Automatic 4-day shutdown mechanism
- Comprehensive logging and error handling
"""

import os
import sys
import time
import signal
import logging
import threading
import subprocess
import json
import shutil
import secrets
import string
import ssl
import socket
import psutil
import pkg_resources
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from queue import Queue, Empty
import hashlib
import zipfile
import tempfile

# Add the project directory to Python path
PROJECT_DIR = Path(__file__).parent
sys.path.insert(0, str(PROJECT_DIR))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gurumisha_project.settings')

# Initialize Django
import django
django.setup()

from django.core.management import execute_from_command_line
from django.contrib.auth import get_user_model
from django.core.wsgi import get_wsgi_application
from django.conf import settings
from core.models import Message, Notification, User
from core.notification_manager import NotificationManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gurumisha_server.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class DependencyManager:
    """Manages Python dependencies and virtual environment"""

    def __init__(self, project_dir):
        self.project_dir = Path(project_dir)
        self.venv_dir = self.project_dir / 'venv'
        self.requirements_file = self.project_dir / 'requirements.txt'

    def check_virtual_environment(self):
        """Check if virtual environment exists and is activated"""
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            logger.info("Virtual environment is active")
            return True

        if self.venv_dir.exists():
            logger.info("Virtual environment exists but not activated")
            return False

        logger.info("No virtual environment found")
        return False

    def create_virtual_environment(self):
        """Create virtual environment if it doesn't exist"""
        try:
            if not self.venv_dir.exists():
                logger.info("Creating virtual environment...")
                subprocess.run([sys.executable, '-m', 'venv', str(self.venv_dir)], check=True)
                logger.info("Virtual environment created successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create virtual environment: {e}")
            return False

    def get_pip_executable(self):
        """Get pip executable path for the virtual environment"""
        if os.name == 'nt':  # Windows
            return self.venv_dir / 'Scripts' / 'pip.exe'
        else:  # Unix/Linux/macOS
            return self.venv_dir / 'bin' / 'pip'

    def install_dependencies(self, upgrade=False):
        """Install dependencies from requirements.txt"""
        try:
            if not self.requirements_file.exists():
                logger.warning("requirements.txt not found, skipping dependency installation")
                return True

            pip_executable = self.get_pip_executable()
            if not pip_executable.exists():
                logger.error("Pip executable not found in virtual environment")
                return False

            cmd = [str(pip_executable), 'install', '-r', str(self.requirements_file)]
            if upgrade:
                cmd.append('--upgrade')

            logger.info("Installing dependencies...")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("Dependencies installed successfully")
                return True
            else:
                logger.error(f"Failed to install dependencies: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return False

    def verify_dependencies(self):
        """Verify all required dependencies are installed"""
        try:
            if not self.requirements_file.exists():
                return True

            with open(self.requirements_file, 'r') as f:
                requirements = f.read().splitlines()

            missing_packages = []
            for requirement in requirements:
                if requirement.strip() and not requirement.startswith('#'):
                    package_name = requirement.split('>=')[0].split('==')[0].split('<')[0].strip()
                    try:
                        pkg_resources.get_distribution(package_name)
                    except pkg_resources.DistributionNotFound:
                        missing_packages.append(package_name)

            if missing_packages:
                logger.warning(f"Missing packages: {missing_packages}")
                return False

            logger.info("All dependencies verified")
            return True

        except Exception as e:
            logger.error(f"Error verifying dependencies: {e}")
            return False

    def upgrade_packages(self):
        """Upgrade outdated packages"""
        try:
            pip_executable = self.get_pip_executable()
            if not pip_executable.exists():
                return False

            # Get list of outdated packages
            result = subprocess.run([str(pip_executable), 'list', '--outdated', '--format=json'],
                                  capture_output=True, text=True)

            if result.returncode == 0 and result.stdout.strip():
                outdated_packages = json.loads(result.stdout)
                if outdated_packages:
                    logger.info(f"Found {len(outdated_packages)} outdated packages")
                    for package in outdated_packages:
                        logger.info(f"Upgrading {package['name']} from {package['version']} to {package['latest_version']}")

                    # Upgrade packages
                    package_names = [pkg['name'] for pkg in outdated_packages]
                    cmd = [str(pip_executable), 'install', '--upgrade'] + package_names
                    upgrade_result = subprocess.run(cmd, capture_output=True, text=True)

                    if upgrade_result.returncode == 0:
                        logger.info("Packages upgraded successfully")
                        return True
                    else:
                        logger.error(f"Failed to upgrade packages: {upgrade_result.stderr}")
                        return False
                else:
                    logger.info("All packages are up to date")
                    return True

            return True

        except Exception as e:
            logger.error(f"Error upgrading packages: {e}")
            return False


class WorkerProcess:
    """Base class for background worker processes"""

    def __init__(self, name, target_function, *args, **kwargs):
        self.name = name
        self.target_function = target_function
        self.args = args
        self.kwargs = kwargs
        self.process = None
        self.is_running = False
        self.last_health_check = None
        self.restart_count = 0
        self.max_restarts = 5

    def start(self):
        """Start the worker process"""
        try:
            self.process = threading.Thread(
                target=self._run_with_monitoring,
                name=f"Worker-{self.name}",
                daemon=True
            )
            self.process.start()
            self.is_running = True
            logger.info(f"Worker process '{self.name}' started")
            return True
        except Exception as e:
            logger.error(f"Failed to start worker process '{self.name}': {e}")
            return False

    def _run_with_monitoring(self):
        """Run the target function with monitoring"""
        try:
            while self.is_running:
                self.last_health_check = datetime.now()
                self.target_function(*self.args, **self.kwargs)
                time.sleep(1)  # Prevent tight loops
        except Exception as e:
            logger.error(f"Worker process '{self.name}' crashed: {e}")
            self._handle_crash()

    def _handle_crash(self):
        """Handle worker process crash and restart if needed"""
        if self.restart_count < self.max_restarts:
            self.restart_count += 1
            logger.info(f"Restarting worker process '{self.name}' (attempt {self.restart_count})")
            time.sleep(5)  # Wait before restart
            self.start()
        else:
            logger.error(f"Worker process '{self.name}' exceeded max restart attempts")
            self.is_running = False

    def stop(self):
        """Stop the worker process"""
        self.is_running = False
        if self.process and self.process.is_alive():
            self.process.join(timeout=10)
        logger.info(f"Worker process '{self.name}' stopped")

    def health_check(self):
        """Check if worker process is healthy"""
        if not self.is_running:
            return False

        if self.last_health_check:
            time_since_check = datetime.now() - self.last_health_check
            if time_since_check.total_seconds() > 300:  # 5 minutes
                logger.warning(f"Worker process '{self.name}' hasn't checked in for {time_since_check}")
                return False

        return True


class GurumishaServer:
    """Main server class for Gurumisha Django application"""
    
    def __init__(self, host='127.0.0.1', port=8000, kill_switch_days=4, auto_init=True):
        self.host = host
        self.port = port
        self.kill_switch_days = kill_switch_days
        self.start_time = datetime.now()
        self.shutdown_time = self.start_time + timedelta(days=kill_switch_days)
        self.server_process = None
        self.kill_switch_thread = None
        self.running = False
        self.auto_init = auto_init

        # Initialize managers
        self.dependency_manager = DependencyManager(PROJECT_DIR)
        self.worker_processes = []
        self.worker_executor = ThreadPoolExecutor(max_workers=10)
        self.backup_retention_days = 7

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info(f"Gurumisha Server initialized")
        logger.info(f"Server will automatically shutdown on: {self.shutdown_time}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown()
    
    def create_admin_user(self, username, email, password, force=False):
        """Create or update admin user"""
        try:
            User = get_user_model()
            
            if User.objects.filter(username=username).exists():
                if not force:
                    logger.warning(f"Admin user '{username}' already exists")
                    return False
                
                user = User.objects.get(username=username)
                user.set_password(password)
                user.email = email
                user.role = 'admin'
                user.is_staff = True
                user.is_superuser = True
                user.is_email_verified = True
                user.save()
                logger.info(f"Admin user '{username}' updated successfully")
            else:
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    role='admin',
                    is_staff=True,
                    is_superuser=True,
                    is_email_verified=True
                )
                logger.info(f"Admin user '{username}' created successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create admin user: {e}")
            return False
    
    def send_system_message(self, title, content, target_audience='all', 
                           message_type='announcement', priority=2):
        """Send system-wide message using existing Message model"""
        try:
            message = Message.objects.create(
                title=title,
                content=content,
                target_audience=target_audience,
                message_type=message_type,
                priority=priority,
                status='active',
                publication_date=datetime.now(),
                created_by_id=1  # Assume first admin user
            )
            
            logger.info(f"System message created: '{title}' for {target_audience}")
            return message
            
        except Exception as e:
            logger.error(f"Failed to send system message: {e}")
            return None
    
    def send_notification_to_users(self, title, message, user_roles=None, channels=None):
        """Send notifications to specific user roles"""
        try:
            User = get_user_model()
            
            if user_roles is None:
                user_roles = ['customer', 'vendor', 'admin']
            
            if channels is None:
                channels = ['in_app', 'email']
            
            users = User.objects.filter(role__in=user_roles, is_active=True)
            
            for user in users:
                NotificationManager.send_notification(
                    recipient=user,
                    title=title,
                    message=message,
                    channels=channels,
                    priority=2
                )
            
            logger.info(f"Notifications sent to {users.count()} users with roles: {user_roles}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send notifications: {e}")
            return False

    def setup_dependencies(self):
        """Setup and verify dependencies"""
        try:
            logger.info("Setting up dependencies...")

            # Check virtual environment
            if not self.dependency_manager.check_virtual_environment():
                if self.auto_init:
                    if not self.dependency_manager.create_virtual_environment():
                        return False
                else:
                    logger.warning("Virtual environment not active. Consider using --auto-init")

            # Verify dependencies
            if not self.dependency_manager.verify_dependencies():
                if self.auto_init:
                    if not self.dependency_manager.install_dependencies():
                        return False
                else:
                    logger.warning("Dependencies missing. Use --install-deps to install")
                    return False

            logger.info("Dependencies setup complete")
            return True

        except Exception as e:
            logger.error(f"Failed to setup dependencies: {e}")
            return False

    def apply_migrations(self):
        """Check and apply pending database migrations"""
        try:
            logger.info("Checking database migrations...")

            # Check for pending migrations
            result = subprocess.run([
                sys.executable, 'manage.py', 'showmigrations', '--plan'
            ], capture_output=True, text=True, cwd=PROJECT_DIR)

            if result.returncode != 0:
                logger.error(f"Failed to check migrations: {result.stderr}")
                return False

            # Check if there are unapplied migrations
            if '[ ]' in result.stdout:
                logger.info("Applying pending migrations...")
                migrate_result = subprocess.run([
                    sys.executable, 'manage.py', 'migrate'
                ], capture_output=True, text=True, cwd=PROJECT_DIR)

                if migrate_result.returncode == 0:
                    logger.info("Migrations applied successfully")
                else:
                    logger.error(f"Failed to apply migrations: {migrate_result.stderr}")
                    return False
            else:
                logger.info("No pending migrations")

            return True

        except Exception as e:
            logger.error(f"Error applying migrations: {e}")
            return False

    def collect_static_files(self):
        """Collect and optimize static files"""
        try:
            logger.info("Collecting static files...")

            result = subprocess.run([
                sys.executable, 'manage.py', 'collectstatic', '--noinput'
            ], capture_output=True, text=True, cwd=PROJECT_DIR)

            if result.returncode == 0:
                logger.info("Static files collected successfully")
                return True
            else:
                logger.error(f"Failed to collect static files: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error collecting static files: {e}")
            return False

    def generate_secure_password(self, length=16):
        """Generate a secure random password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password

    def validate_environment(self):
        """Validate environment configuration"""
        try:
            logger.info("Validating environment configuration...")

            # Check required environment variables
            required_vars = ['DJANGO_SETTINGS_MODULE']
            missing_vars = []

            for var in required_vars:
                if not os.environ.get(var):
                    missing_vars.append(var)

            if missing_vars:
                logger.warning(f"Missing environment variables: {missing_vars}")

            # Check database connectivity
            from django.db import connection
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                logger.info("Database connection validated")
            except Exception as e:
                logger.error(f"Database connection failed: {e}")
                return False

            # Check static files directory
            static_root = getattr(settings, 'STATIC_ROOT', None)
            if static_root and not Path(static_root).exists():
                logger.warning(f"Static root directory does not exist: {static_root}")

            # Check media files directory
            media_root = getattr(settings, 'MEDIA_ROOT', None)
            if media_root:
                Path(media_root).mkdir(parents=True, exist_ok=True)
                logger.info("Media directory validated")

            logger.info("Environment validation complete")
            return True

        except Exception as e:
            logger.error(f"Environment validation failed: {e}")
            return False

    def setup_ssl_certificate(self, cert_path=None, key_path=None):
        """Setup SSL certificate for production deployment"""
        try:
            if not cert_path or not key_path:
                logger.info("No SSL certificate paths provided, skipping SSL setup")
                return True

            cert_file = Path(cert_path)
            key_file = Path(key_path)

            if not cert_file.exists():
                logger.error(f"SSL certificate file not found: {cert_path}")
                return False

            if not key_file.exists():
                logger.error(f"SSL key file not found: {key_path}")
                return False

            # Validate certificate
            try:
                with open(cert_file, 'r') as f:
                    cert_data = f.read()
                ssl.PEM_cert_to_DER_cert(cert_data)
                logger.info("SSL certificate validated")
                return True
            except Exception as e:
                logger.error(f"Invalid SSL certificate: {e}")
                return False

        except Exception as e:
            logger.error(f"SSL setup failed: {e}")
            return False

    def setup_worker_processes(self):
        """Setup and start background worker processes"""
        try:
            logger.info("Setting up worker processes...")

            # Email notification worker
            email_worker = WorkerProcess(
                "EmailNotificationWorker",
                self._email_notification_worker
            )
            self.worker_processes.append(email_worker)

            # Database maintenance worker
            db_worker = WorkerProcess(
                "DatabaseMaintenanceWorker",
                self._database_maintenance_worker
            )
            self.worker_processes.append(db_worker)

            # Import/Export processing worker
            import_worker = WorkerProcess(
                "ImportExportWorker",
                self._import_export_worker
            )
            self.worker_processes.append(import_worker)

            # Scheduled tasks worker
            scheduler_worker = WorkerProcess(
                "ScheduledTasksWorker",
                self._scheduled_tasks_worker
            )
            self.worker_processes.append(scheduler_worker)

            # Performance monitoring worker
            monitor_worker = WorkerProcess(
                "PerformanceMonitorWorker",
                self._performance_monitor_worker
            )
            self.worker_processes.append(monitor_worker)

            # Start all workers
            for worker in self.worker_processes:
                worker.start()

            logger.info(f"Started {len(self.worker_processes)} worker processes")
            return True

        except Exception as e:
            logger.error(f"Failed to setup worker processes: {e}")
            return False

    def _email_notification_worker(self):
        """Worker process for handling email notifications"""
        try:
            from core.models import NotificationQueue

            # Process pending email notifications
            pending_notifications = NotificationQueue.objects.filter(
                status='pending',
                channel='email'
            ).order_by('priority', 'created_at')[:10]

            for notification in pending_notifications:
                try:
                    # Update status to processing
                    notification.status = 'processing'
                    notification.save()

                    # Send email using Django's email system
                    from django.core.mail import send_mail

                    send_mail(
                        subject=notification.subject,
                        message=notification.message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[notification.recipient.email],
                        fail_silently=False
                    )

                    # Update status to sent
                    notification.status = 'sent'
                    notification.sent_at = datetime.now()
                    notification.save()

                    logger.debug(f"Email sent to {notification.recipient.email}")

                except Exception as e:
                    notification.status = 'failed'
                    notification.save()
                    logger.error(f"Failed to send email to {notification.recipient.email}: {e}")

            time.sleep(30)  # Check every 30 seconds

        except Exception as e:
            logger.error(f"Email notification worker error: {e}")
            time.sleep(60)  # Wait longer on error

    def _database_maintenance_worker(self):
        """Worker process for database maintenance tasks"""
        try:
            from django.db import connection

            # Run database optimization every hour
            current_time = datetime.now()
            if current_time.minute == 0:  # Top of the hour
                logger.info("Running database maintenance...")

                # Vacuum SQLite database (if using SQLite)
                if 'sqlite' in settings.DATABASES['default']['ENGINE']:
                    with connection.cursor() as cursor:
                        cursor.execute("VACUUM;")
                    logger.info("Database vacuum completed")

                # Clean up old log entries
                self._cleanup_old_logs()

                # Clean up old backups
                self._cleanup_old_backups()

            time.sleep(60)  # Check every minute

        except Exception as e:
            logger.error(f"Database maintenance worker error: {e}")
            time.sleep(300)  # Wait 5 minutes on error

    def _import_export_worker(self):
        """Worker process for import/export order processing"""
        try:
            from core.models import ImportOrder, ImportRequest

            # Process import orders that need status updates
            active_orders = ImportOrder.objects.filter(
                status__in=['confirmed', 'auction_won', 'shipped', 'in_transit', 'arrived_docked']
            )

            for order in active_orders:
                # Simulate status progression (in real implementation, this would check external APIs)
                if order.status == 'confirmed' and self._should_update_status(order):
                    order.status = 'auction_won'
                    order.save()

                    # Send notification
                    self.send_notification_to_users(
                        title=f"Import Order Update - {order.order_number}",
                        message=f"Your import order has won the auction!",
                        user_roles=['customer'],
                        channels=['email', 'in_app']
                    )

            time.sleep(300)  # Check every 5 minutes

        except Exception as e:
            logger.error(f"Import/Export worker error: {e}")
            time.sleep(600)  # Wait 10 minutes on error

    def _scheduled_tasks_worker(self):
        """Worker process for scheduled tasks"""
        try:
            current_time = datetime.now()

            # Daily reports at midnight
            if current_time.hour == 0 and current_time.minute == 0:
                self._generate_daily_reports()

            # Weekly analytics on Mondays at 1 AM
            if current_time.weekday() == 0 and current_time.hour == 1 and current_time.minute == 0:
                self._generate_weekly_analytics()

            # Backup database every 6 hours
            if current_time.hour % 6 == 0 and current_time.minute == 0:
                ServerManager.backup_database()

            time.sleep(60)  # Check every minute

        except Exception as e:
            logger.error(f"Scheduled tasks worker error: {e}")
            time.sleep(300)  # Wait 5 minutes on error

    def _performance_monitor_worker(self):
        """Worker process for performance monitoring"""
        try:
            # Monitor system resources
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Log performance metrics
            if cpu_percent > 80:
                logger.warning(f"High CPU usage: {cpu_percent}%")

            if memory.percent > 80:
                logger.warning(f"High memory usage: {memory.percent}%")

            if disk.percent > 90:
                logger.warning(f"High disk usage: {disk.percent}%")

            # Monitor Django database connections
            from django.db import connections
            for alias in connections:
                connection = connections[alias]
                if hasattr(connection, 'queries'):
                    query_count = len(connection.queries)
                    if query_count > 100:
                        logger.warning(f"High query count on {alias}: {query_count}")

            time.sleep(60)  # Monitor every minute

        except Exception as e:
            logger.error(f"Performance monitor worker error: {e}")
            time.sleep(120)  # Wait 2 minutes on error

    def _should_update_status(self, order):
        """Determine if an import order status should be updated"""
        # Simple time-based logic for demo (replace with real API checks)
        time_since_update = datetime.now() - order.updated_at
        return time_since_update.total_seconds() > 3600  # 1 hour

    def _generate_daily_reports(self):
        """Generate daily analytics reports"""
        try:
            logger.info("Generating daily reports...")

            # Get daily statistics
            User = get_user_model()
            from core.models import Car, Order, ImportRequest

            today = datetime.now().date()
            stats = {
                'date': today.isoformat(),
                'new_users': User.objects.filter(date_joined__date=today).count(),
                'new_cars': Car.objects.filter(created_at__date=today).count(),
                'new_orders': Order.objects.filter(created_at__date=today).count(),
                'new_imports': ImportRequest.objects.filter(created_at__date=today).count(),
            }

            # Save report to file
            reports_dir = PROJECT_DIR / 'reports'
            reports_dir.mkdir(exist_ok=True)

            report_file = reports_dir / f'daily_report_{today.isoformat()}.json'
            with open(report_file, 'w') as f:
                json.dump(stats, f, indent=2)

            logger.info(f"Daily report saved: {report_file}")

        except Exception as e:
            logger.error(f"Failed to generate daily reports: {e}")

    def _generate_weekly_analytics(self):
        """Generate weekly analytics"""
        try:
            logger.info("Generating weekly analytics...")

            # Implementation for weekly analytics
            # This would include more comprehensive analysis

        except Exception as e:
            logger.error(f"Failed to generate weekly analytics: {e}")

    def _cleanup_old_logs(self):
        """Clean up old log entries"""
        try:
            from core.models import ActivityLog, AuditLog

            # Remove logs older than 30 days
            cutoff_date = datetime.now() - timedelta(days=30)

            deleted_activity = ActivityLog.objects.filter(timestamp__lt=cutoff_date).delete()
            deleted_audit = AuditLog.objects.filter(timestamp__lt=cutoff_date).delete()

            logger.info(f"Cleaned up {deleted_activity[0]} activity logs and {deleted_audit[0]} audit logs")

        except Exception as e:
            logger.error(f"Failed to cleanup old logs: {e}")

    def _cleanup_old_backups(self):
        """Clean up old backup files"""
        try:
            backup_dir = PROJECT_DIR / 'backups'
            if not backup_dir.exists():
                return

            cutoff_date = datetime.now() - timedelta(days=self.backup_retention_days)

            for backup_file in backup_dir.glob('db_backup_*.json'):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    logger.info(f"Removed old backup: {backup_file}")

        except Exception as e:
            logger.error(f"Failed to cleanup old backups: {e}")

    def setup_log_rotation(self):
        """Setup log rotation and cleanup"""
        try:
            from logging.handlers import RotatingFileHandler

            # Setup rotating file handler
            log_file = PROJECT_DIR / 'gurumisha_server.log'
            handler = RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )

            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)

            # Add to root logger
            root_logger = logging.getLogger()
            root_logger.addHandler(handler)

            logger.info("Log rotation setup complete")
            return True

        except Exception as e:
            logger.error(f"Failed to setup log rotation: {e}")
            return False

    def perform_security_scan(self):
        """Perform basic security checks"""
        try:
            logger.info("Performing security scan...")

            security_issues = []

            # Check for debug mode in production
            if getattr(settings, 'DEBUG', False):
                security_issues.append("DEBUG mode is enabled")

            # Check for default secret key
            secret_key = getattr(settings, 'SECRET_KEY', '')
            if 'django-insecure' in secret_key or len(secret_key) < 50:
                security_issues.append("Weak or default SECRET_KEY")

            # Check for HTTPS settings
            if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
                security_issues.append("SECURE_SSL_REDIRECT not enabled")

            # Check allowed hosts
            allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
            if '*' in allowed_hosts:
                security_issues.append("Wildcard in ALLOWED_HOSTS")

            # Check for default admin credentials
            User = get_user_model()
            default_admin = User.objects.filter(username='admin').first()
            if default_admin and default_admin.check_password('admin123'):
                security_issues.append("Default admin credentials detected")

            if security_issues:
                logger.warning("Security issues found:")
                for issue in security_issues:
                    logger.warning(f"  - {issue}")
            else:
                logger.info("No security issues detected")

            return len(security_issues) == 0

        except Exception as e:
            logger.error(f"Security scan failed: {e}")
            return False

    def monitor_worker_health(self):
        """Monitor worker process health and restart if needed"""
        try:
            for worker in self.worker_processes:
                if not worker.health_check():
                    logger.warning(f"Worker '{worker.name}' failed health check, restarting...")
                    worker.stop()
                    worker.start()

        except Exception as e:
            logger.error(f"Worker health monitoring failed: {e}")

    def initialize_sample_data(self):
        """Initialize sample data if database is empty"""
        try:
            User = get_user_model()
            from core.models import CarBrand, VehicleCondition

            # Check if we need sample data
            if User.objects.count() > 1:  # More than just admin
                logger.info("Database already has data, skipping sample data initialization")
                return True

            logger.info("Initializing sample data...")

            # Create sample car brands
            brands = ['Toyota', 'Honda', 'Nissan', 'Mercedes-Benz', 'BMW']
            for brand_name in brands:
                CarBrand.objects.get_or_create(name=brand_name)

            # Create vehicle conditions
            conditions = [
                ('new', 'New'),
                ('used', 'Used'),
                ('certified', 'Certified Pre-Owned')
            ]
            for code, name in conditions:
                VehicleCondition.objects.get_or_create(code=code, defaults={'name': name})

            logger.info("Sample data initialized")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize sample data: {e}")
            return False
    
    def _kill_switch_monitor(self):
        """Monitor for kill switch activation"""
        while self.running:
            current_time = datetime.now()
            time_remaining = self.shutdown_time - current_time
            
            if time_remaining.total_seconds() <= 0:
                logger.critical("KILL SWITCH ACTIVATED - 4 days elapsed, shutting down server")
                self._send_shutdown_notifications()
                self.shutdown()
                break
            
            # Log remaining time every hour
            if time_remaining.total_seconds() % 3600 < 60:
                hours_remaining = int(time_remaining.total_seconds() // 3600)
                logger.info(f"Kill switch countdown: {hours_remaining} hours remaining")
            
            time.sleep(60)  # Check every minute
    
    def _send_shutdown_notifications(self):
        """Send notifications before shutdown"""
        try:
            # Send system message
            self.send_system_message(
                title="Server Maintenance Notice",
                content="The server will be shutting down for scheduled maintenance. Thank you for your patience.",
                target_audience='all',
                message_type='maintenance',
                priority=4
            )
            
            # Send notifications to all users
            self.send_notification_to_users(
                title="Server Maintenance",
                message="The Gurumisha platform will be temporarily unavailable for maintenance.",
                user_roles=['customer', 'vendor', 'admin'],
                channels=['in_app', 'email']
            )
            
            logger.info("Shutdown notifications sent successfully")
            
        except Exception as e:
            logger.error(f"Failed to send shutdown notifications: {e}")
    
    def start(self):
        """Start the Django server with enhanced initialization"""
        try:
            logger.info("Starting Gurumisha Django Server with enhanced features...")
            logger.info(f"Server URL: http://{self.host}:{self.port}")
            logger.info(f"Admin Panel: http://{self.host}:{self.port}/admin/")
            logger.info(f"Kill switch will activate in {self.kill_switch_days} days")

            # Enhanced initialization process
            if self.auto_init:
                logger.info("Running enhanced initialization...")

                # Setup dependencies
                if not self.setup_dependencies():
                    logger.error("Dependency setup failed")
                    return

                # Apply migrations
                if not self.apply_migrations():
                    logger.error("Migration application failed")
                    return

                # Collect static files
                if not self.collect_static_files():
                    logger.warning("Static file collection failed, continuing...")

                # Validate environment
                if not self.validate_environment():
                    logger.error("Environment validation failed")
                    return

                # Setup log rotation
                self.setup_log_rotation()

                # Perform security scan
                self.perform_security_scan()

                # Initialize sample data if needed
                self.initialize_sample_data()

            # Create default admin user with secure password if needed
            admin_password = self.generate_secure_password()
            admin_created = self.create_admin_user(
                username='admin',
                email='<EMAIL>',
                password=admin_password,
                force=False
            )

            if admin_created:
                logger.info(f"Default admin created with password: {admin_password}")
                logger.warning("Please change the default admin password immediately!")

            # Setup worker processes
            if not self.setup_worker_processes():
                logger.warning("Worker process setup failed, continuing without workers...")

            # Start kill switch monitor in separate thread
            self.running = True
            self.kill_switch_thread = threading.Thread(target=self._kill_switch_monitor)
            self.kill_switch_thread.daemon = True
            self.kill_switch_thread.start()

            # Start worker health monitoring
            health_monitor_thread = threading.Thread(target=self._worker_health_monitor)
            health_monitor_thread.daemon = True
            health_monitor_thread.start()

            # Start Django development server
            self.server_process = subprocess.Popen([
                sys.executable, 'manage.py', 'runserver', f'{self.host}:{self.port}'
            ], cwd=PROJECT_DIR)

            logger.info("Django server started successfully")
            logger.info("All systems operational - server ready for requests")

            # Wait for server process
            self.server_process.wait()

        except KeyboardInterrupt:
            logger.info("Server interrupted by user")
        except Exception as e:
            logger.error(f"Failed to start server: {e}")
        finally:
            self.shutdown()

    def _worker_health_monitor(self):
        """Background thread to monitor worker health"""
        while self.running:
            try:
                self.monitor_worker_health()
                time.sleep(300)  # Check every 5 minutes
            except Exception as e:
                logger.error(f"Worker health monitor error: {e}")
                time.sleep(60)
    
    def shutdown(self):
        """Graceful server shutdown with enhanced cleanup"""
        if not self.running:
            return

        logger.info("Initiating enhanced server shutdown...")
        self.running = False

        try:
            # Stop worker processes
            logger.info("Stopping worker processes...")
            for worker in self.worker_processes:
                worker.stop()

            # Shutdown worker executor
            self.worker_executor.shutdown(wait=True, timeout=30)

            # Terminate Django server process
            if self.server_process and self.server_process.poll() is None:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                logger.info("Django server process terminated")

        except subprocess.TimeoutExpired:
            logger.warning("Server process did not terminate gracefully, forcing shutdown")
            self.server_process.kill()
        except Exception as e:
            logger.error(f"Error during server shutdown: {e}")

        # Final backup before shutdown
        try:
            backup_file = ServerManager.backup_database()
            if backup_file:
                logger.info(f"Final backup created: {backup_file}")
        except Exception as e:
            logger.error(f"Failed to create final backup: {e}")

        # Calculate uptime
        uptime = datetime.now() - self.start_time
        logger.info(f"Server uptime: {uptime}")
        logger.info("Enhanced Gurumisha server shutdown complete")


def main():
    """Enhanced main entry point with comprehensive options"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Gurumisha Django Server - Enhanced Enterprise Edition',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python server.py                          # Start with default settings
  python server.py --auto-init             # Start with full initialization
  python server.py --install-deps          # Install dependencies only
  python server.py --upgrade-deps          # Upgrade all dependencies
  python server.py --security-scan         # Run security scan only
  python server.py --backup-db             # Create database backup only
        """
    )

    # Server configuration
    parser.add_argument('--host', default='127.0.0.1',
                       help='Server host (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=8000,
                       help='Server port (default: 8000)')
    parser.add_argument('--kill-switch-days', type=int, default=4,
                       help='Days until automatic shutdown (default: 4)')

    # Initialization options
    parser.add_argument('--auto-init', action='store_true',
                       help='Enable automatic initialization and setup')
    parser.add_argument('--no-workers', action='store_true',
                       help='Disable background worker processes')
    parser.add_argument('--ssl-cert', help='Path to SSL certificate file')
    parser.add_argument('--ssl-key', help='Path to SSL private key file')

    # Dependency management
    parser.add_argument('--install-deps', action='store_true',
                       help='Install dependencies from requirements.txt')
    parser.add_argument('--upgrade-deps', action='store_true',
                       help='Upgrade all outdated packages')
    parser.add_argument('--verify-deps', action='store_true',
                       help='Verify all dependencies are installed')

    # Database operations
    parser.add_argument('--migrate', action='store_true',
                       help='Apply database migrations only')
    parser.add_argument('--backup-db', action='store_true',
                       help='Create database backup only')
    parser.add_argument('--collect-static', action='store_true',
                       help='Collect static files only')

    # Admin and messaging
    parser.add_argument('--create-admin', action='store_true',
                       help='Create admin user interactively')
    parser.add_argument('--send-message', action='store_true',
                       help='Send system message interactively')
    parser.add_argument('--secure-admin', action='store_true',
                       help='Create admin with secure generated password')

    # Maintenance operations
    parser.add_argument('--security-scan', action='store_true',
                       help='Run security vulnerability scan')
    parser.add_argument('--health-check', action='store_true',
                       help='Run system health check')
    parser.add_argument('--cleanup', action='store_true',
                       help='Clean up old logs and backups')
    parser.add_argument('--stats', action='store_true',
                       help='Show system statistics')

    args = parser.parse_args()

    # Create server instance
    server = GurumishaServer(
        host=args.host,
        port=args.port,
        kill_switch_days=args.kill_switch_days,
        auto_init=args.auto_init
    )

    # Handle dependency management operations
    if args.install_deps:
        success = server.dependency_manager.install_dependencies()
        sys.exit(0 if success else 1)

    if args.upgrade_deps:
        success = server.dependency_manager.upgrade_packages()
        sys.exit(0 if success else 1)

    if args.verify_deps:
        success = server.dependency_manager.verify_dependencies()
        sys.exit(0 if success else 1)

    # Handle database operations
    if args.migrate:
        success = server.apply_migrations()
        sys.exit(0 if success else 1)

    if args.backup_db:
        backup_file = ServerManager.backup_database()
        if backup_file:
            print(f"Backup created: {backup_file}")
            sys.exit(0)
        else:
            sys.exit(1)

    if args.collect_static:
        success = server.collect_static_files()
        sys.exit(0 if success else 1)

    # Handle maintenance operations
    if args.security_scan:
        success = server.perform_security_scan()
        sys.exit(0 if success else 1)

    if args.health_check:
        success = ServerManager.check_system_health()
        sys.exit(0 if success else 1)

    if args.cleanup:
        server._cleanup_old_logs()
        server._cleanup_old_backups()
        print("Cleanup completed")
        sys.exit(0)

    if args.stats:
        stats = ServerManager.get_server_stats()
        print("System Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        sys.exit(0)

    # Handle admin operations
    if args.create_admin:
        username = input("Admin username: ")
        email = input("Admin email: ")
        password = input("Admin password: ")
        success = server.create_admin_user(username, email, password, force=True)
        sys.exit(0 if success else 1)

    if args.secure_admin:
        username = input("Admin username: ")
        email = input("Admin email: ")
        password = server.generate_secure_password()
        success = server.create_admin_user(username, email, password, force=True)
        if success:
            print(f"Admin created with secure password: {password}")
            print("Please save this password securely!")
        sys.exit(0 if success else 1)

    if args.send_message:
        title = input("Message title: ")
        content = input("Message content: ")
        audience = input("Target audience (all/customers/vendors/admins): ") or 'all'
        message = server.send_system_message(title, content, audience)
        sys.exit(0 if message else 1)

    # Setup SSL if provided
    if args.ssl_cert and args.ssl_key:
        if not server.setup_ssl_certificate(args.ssl_cert, args.ssl_key):
            logger.error("SSL setup failed")
            sys.exit(1)

    # Disable workers if requested
    if args.no_workers:
        server.worker_processes = []

    # Start the server
    server.start()


class ServerManager:
    """Additional server management utilities"""

    @staticmethod
    def check_system_health():
        """Check system health and database connectivity"""
        try:
            from django.db import connection
            from django.core.management import call_command

            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")

            # Check for pending migrations
            try:
                call_command('showmigrations', '--plan', verbosity=0)
                logger.info("Database health check passed")
                return True
            except Exception as e:
                logger.error(f"Migration check failed: {e}")
                return False

        except Exception as e:
            logger.error(f"System health check failed: {e}")
            return False

    @staticmethod
    def backup_database():
        """Create database backup"""
        try:
            backup_dir = PROJECT_DIR / 'backups'
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_dir / f'db_backup_{timestamp}.json'

            # Use Django's dumpdata command
            with open(backup_file, 'w') as f:
                subprocess.run([
                    sys.executable, 'manage.py', 'dumpdata',
                    '--natural-foreign', '--natural-primary'
                ], stdout=f, cwd=PROJECT_DIR, check=True)

            logger.info(f"Database backup created: {backup_file}")
            return str(backup_file)

        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            return None

    @staticmethod
    def get_server_stats():
        """Get server statistics"""
        try:
            User = get_user_model()
            from core.models import Car, SparePart, ImportRequest, Order

            stats = {
                'total_users': User.objects.count(),
                'active_users': User.objects.filter(is_active=True).count(),
                'customers': User.objects.filter(role='customer').count(),
                'vendors': User.objects.filter(role='vendor').count(),
                'admins': User.objects.filter(role='admin').count(),
                'total_cars': Car.objects.count(),
                'total_spare_parts': SparePart.objects.count(),
                'total_import_requests': ImportRequest.objects.count(),
                'total_orders': Order.objects.count(),
                'unread_notifications': Notification.objects.filter(is_read=False).count(),
                'active_messages': Message.objects.filter(status='active').count(),
            }

            return stats

        except Exception as e:
            logger.error(f"Failed to get server stats: {e}")
            return {}


def setup_server_environment():
    """Setup server environment and perform initial checks"""
    logger.info("Setting up server environment...")

    # Check if we're in the correct directory
    if not (PROJECT_DIR / 'manage.py').exists():
        logger.error("manage.py not found. Please run server.py from the project root.")
        sys.exit(1)

    # Check database
    if not ServerManager.check_system_health():
        logger.error("System health check failed. Please check your database configuration.")
        sys.exit(1)

    # Create backup before starting
    backup_file = ServerManager.backup_database()
    if backup_file:
        logger.info(f"Pre-startup backup created: {backup_file}")

    # Log server statistics
    stats = ServerManager.get_server_stats()
    if stats:
        logger.info("Current server statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")

    logger.info("Server environment setup complete")


if __name__ == '__main__':
    # Setup environment before starting
    setup_server_environment()
    main()
